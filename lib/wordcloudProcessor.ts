import { type PostInfo, getPostsAsJson } from './postDetection';
import { generateText, initializeOpenAI } from './openaiService';
import { loadOpenAIConfig } from './openaiConfig';
import { extractAndParseJSON } from './utils';

/**
 * 检测并处理网页中的文章列表
 * @returns 返回处理后的关键词数据
 */
export async function processPagePosts() {
  try {
    console.log('[词云处理] 开始处理网页文章列表...');
    
    // 1. 获取网页中的文章列表
    const posts = getPostsAsJson();
    
    if (!posts || posts.length === 0) {
      console.log('[词云处理] 未检测到文章列表');
      return null;
    }
    
    console.log(`[词云处理] 检测到 ${posts.length} 篇文章`);
    
    // 2. 准备发送给OpenAI的数据 - 简化为仅包含id和title
    const simplifiedPosts = posts.map((post, index) => ({
      id: index + 1, // 添加自增ID
      title: post.title
    }));
    
    // 创建ID到完整帖子信息的映射，用于后续处理
    const fullPostsMap = new Map();
    posts.forEach((post, index) => {
      fullPostsMap.set(index + 1, {
        id: index + 1,
        title: post.title,
        link: post.link,
        author: post.author,
        time: post.time,
        xpath: post.xpath
      });
    });
    
    console.log('[词云处理] 准备的简化文章数据:', simplifiedPosts);
    
    // 3. 读取提示词模板
    const promptTemplate = await loadPromptTemplate();
    
    // 4. 替换占位符，生成完整提示词
    const fullPrompt = promptTemplate.replace('{{posts}}', JSON.stringify(simplifiedPosts, null, 2));
    console.log('[词云处理] 生成的完整提示词:', fullPrompt);
    
    // 5. 加载OpenAI配置并初始化服务
    const config = await loadOpenAIConfig();
    if (!config.apiKey) {
      console.error('[词云处理] 未配置OpenAI API密钥');
      throw new Error('未配置OpenAI API密钥，请在设置中配置');
    }
    
    // 初始化OpenAI服务
    console.log('[词云处理] 初始化OpenAI服务...');
    initializeOpenAI(config.apiKey, config.baseURL);
    
    console.log('[词云处理] 开始调用OpenAI API...');
    const response = await generateText(fullPrompt, config.model);
    
    // 6. 解析OpenAI的响应
    const keywordsData = parseOpenAIResponse(response);
    console.log('[词云处理] 解析到的关键词数据:', keywordsData);
    
    // 7. 为每个关键词添加原始帖子完整信息
    const enhancedKeywordsData = keywordsData.map(keyword => {
      // 获取关联帖子的完整信息
      const originalPosts = keyword.ids.map(id => {
        // 使用完整帖子映射获取所有字段
        const fullPost = fullPostsMap.get(id);
        return fullPost || { id, title: `文章 #${id}` };
      });
      
      return {
        ...keyword,
        originalPosts
      };
    });
    
    console.log('[词云处理] 增强后的关键词数据:', enhancedKeywordsData);
    
    return enhancedKeywordsData;
  } catch (error) {
    console.error('[词云处理] 处理失败:', error);
    throw error;
  }
}

/**
 * 加载提示词模板
 */
async function loadPromptTemplate(): Promise<string> {
  try {
    const templateUrl = chrome.runtime.getURL('assets/prompt_parse.md');
    const response = await fetch(templateUrl);
    
    if (!response.ok) {
      throw new Error(`无法加载提示词模板: ${response.status}`);
    }
    
    const template = await response.text();
    return template;
  } catch (error) {
    console.error('[词云处理] 加载提示词模板失败:', error);
    throw error;
  }
}

/**
 * 解析OpenAI的响应，提取关键词数据
 * @param response OpenAI的响应文本
 * @returns 关键词数据
 */
function parseOpenAIResponse(response: string): any {
  try {
    // 使用通用工具函数提取和解析JSON
    const parsedData = extractAndParseJSON(response, []);
    
    if (!parsedData || (Array.isArray(parsedData) && parsedData.length === 0)) {
      console.warn('[词云处理] 解析结果为空');
    }
    
    return parsedData;
  } catch (error) {
    console.error('[词云处理] 解析OpenAI响应失败:', error);
    console.log('原始响应:', response);
    throw new Error('无法解析OpenAI的响应，不是有效的JSON格式');
  }
} 