import OpenAI from 'openai';

// OpenAI 服务类
export class OpenAIService {
  private client: OpenAI;

  constructor(apiKey: string, baseURL?: string) {
    this.client = new OpenAI({
      apiKey,
      baseURL,
      dangerouslyAllowBrowser: true // 允许在浏览器环境中使用
    });
  }

  /**
   * 生成聊天回复
   * @param messages 消息历史
   * @param model 模型名称，默认为 gpt-3.5-turbo
   * @param maxTokens 最大token数
   * @returns 生成的回复
   */
  async generateChatCompletion(
    messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    model: string = 'gpt-3.5-turbo',
    maxTokens: number = 1000
  ): Promise<string> {
    try {
      const completion = await this.client.chat.completions.create({
        model,
        messages,
        max_tokens: maxTokens,
        temperature: 0.7,
      });

      return completion.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenAI API 调用失败:', error);
      throw error;
    }
  }

  /**
   * 生成文本内容
   * @param prompt 提示词
   * @param model 模型名称
   * @param maxTokens 最大token数
   * @returns 生成的文本
   */
  async generateText(
    prompt: string,
    model: string = 'gpt-3.5-turbo',
    maxTokens: number = 1000
  ): Promise<string> {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'user', content: prompt }
    ];

    return this.generateChatCompletion(messages, model, maxTokens);
  }

  /**
   * 流式生成聊天回复
   * @param messages 消息历史
   * @param model 模型名称
   * @param onChunk 接收流式数据的回调函数
   * @param maxTokens 最大token数
   */
  async generateChatCompletionStream(
    messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    model: string = 'gpt-3.5-turbo',
    onChunk: (chunk: string) => void,
    maxTokens: number = 1000
  ): Promise<void> {
    try {
      const stream = await this.client.chat.completions.create({
        model,
        messages,
        max_tokens: maxTokens,
        temperature: 0.7,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          onChunk(content);
        }
      }
    } catch (error) {
      console.error('OpenAI 流式API 调用失败:', error);
      throw error;
    }
  }
}

// 创建默认的 OpenAI 服务实例
let defaultOpenAIService: OpenAIService | null = null;

/**
 * 初始化默认的 OpenAI 服务
 * @param apiKey API密钥
 * @param baseURL 可选的基础URL
 */
export function initializeOpenAI(apiKey: string, baseURL?: string): void {
  defaultOpenAIService = new OpenAIService(apiKey, baseURL);
}

/**
 * 获取默认的 OpenAI 服务实例
 * @returns OpenAI 服务实例
 */
export function getOpenAIService(): OpenAIService {
  if (!defaultOpenAIService) {
    throw new Error('OpenAI 服务未初始化，请先调用 initializeOpenAI()');
  }
  return defaultOpenAIService;
}

// 便捷函数
export async function generateText(prompt: string, model?: string): Promise<string> {
  return getOpenAIService().generateText(prompt, model);
}

export async function generateChatCompletion(
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  model?: string
): Promise<string> {
  return getOpenAIService().generateChatCompletion(messages, model);
}
