import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { jsonrepair } from 'jsonrepair';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 安全解析JSON字符串，使用jsonrepair修复可能存在的格式问题
 * @param jsonString 要解析的JSON字符串
 * @param defaultValue 解析失败时返回的默认值
 * @returns 解析后的对象，或解析失败时的默认值
 */
export function safeParseJSON<T>(jsonString: string, defaultValue: T = null as unknown as T): T {
  try {
    if (!jsonString || typeof jsonString !== 'string') {
      return defaultValue;
    }
    
    // 使用jsonrepair修复可能存在格式问题的JSON
    const repairedJson = jsonrepair(jsonString);
    
    // 解析修复后的JSON
    return JSON.parse(repairedJson);
  } catch (error) {
    console.error('JSON解析失败:', error);
    console.log('原始JSON字符串:', jsonString);
    return defaultValue;
  }
}

/**
 * 从文本中提取并解析JSON
 * 支持从代码块或整个文本中提取
 * @param text 包含JSON的文本
 * @param defaultValue 解析失败时返回的默认值
 * @returns 解析后的对象，或解析失败时的默认值
 */
export function extractAndParseJSON<T>(text: string, defaultValue: T = null as unknown as T): T {
  try {
    if (!text || typeof text !== 'string') {
      return defaultValue;
    }
    
    // 尝试从文本中提取JSON部分
    const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) || 
                      text.match(/```\n([\s\S]*?)\n```/) ||
                      text.match(/\[([\s\S]*?)\]/) ||
                      text.match(/\{([\s\S]*?)\}/);
    
    let jsonStr = '';
    if (jsonMatch && jsonMatch[0]) {
      // 如果匹配到了代码块格式，使用匹配到的内容
      // 对于数组和对象匹配，我们使用完整匹配结果
      jsonStr = jsonMatch[0].startsWith('```') ? jsonMatch[1] : jsonMatch[0];
    } else {
      // 如果没有匹配到特定格式，使用整个文本
      jsonStr = text;
    }
    
    // 使用safeParseJSON解析
    return safeParseJSON(jsonStr, defaultValue);
  } catch (error) {
    console.error('JSON提取和解析失败:', error);
    console.log('原始文本:', text);
    return defaultValue;
  }
} 