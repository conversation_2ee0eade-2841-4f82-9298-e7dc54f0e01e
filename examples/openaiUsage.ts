/**
 * OpenAI 使用示例
 * 展示如何在浏览器扩展中使用 OpenAI API
 */

import { OpenAIService, initializeOpenAI, generateText } from '../lib/openaiService';
import { loadOpenAIConfig, saveOpenAIConfig, OPENAI_MODELS } from '../lib/openaiConfig';

// 示例1: 初始化和基本使用
export async function basicUsageExample() {
  try {
    // 从存储中加载配置
    const config = await loadOpenAIConfig();
    
    if (!config.apiKey) {
      console.error('请先设置 OpenAI API Key');
      return;
    }

    // 初始化 OpenAI 服务
    initializeOpenAI(config.apiKey, config.baseURL);

    // 生成简单文本
    const response = await generateText('请帮我写一个关于人工智能的简短介绍');
    console.log('AI 回复:', response);

  } catch (error) {
    console.error('OpenAI 调用失败:', error);
  }
}

// 示例2: 聊天对话
export async function chatExample() {
  try {
    const config = await loadOpenAIConfig();
    const service = new OpenAIService(config.apiKey!, config.baseURL);

    const messages = [
      { role: 'system' as const, content: '你是一个有用的助手，专门帮助用户解决问题。' },
      { role: 'user' as const, content: '请解释什么是机器学习？' }
    ];

    const response = await service.generateChatCompletion(messages, config.model);
    console.log('聊天回复:', response);

  } catch (error) {
    console.error('聊天失败:', error);
  }
}

// 示例3: 流式生成
export async function streamExample() {
  try {
    const config = await loadOpenAIConfig();
    const service = new OpenAIService(config.apiKey!, config.baseURL);

    const messages = [
      { role: 'user' as const, content: '请写一个关于春天的诗歌' }
    ];

    let fullResponse = '';
    
    await service.generateChatCompletionStream(
      messages,
      config.model,
      (chunk) => {
        fullResponse += chunk;
        console.log('流式数据:', chunk);
      }
    );

    console.log('完整回复:', fullResponse);

  } catch (error) {
    console.error('流式生成失败:', error);
  }
}

// 示例4: 内容生成（适用于刷帖场景）
export async function generatePostContent(topic: string, style: string = '友好') {
  try {
    const config = await loadOpenAIConfig();
    const service = new OpenAIService(config.apiKey!, config.baseURL);

    const prompt = `请根据以下主题生成一个${style}的帖子内容：
主题: ${topic}

要求：
1. 内容要自然、有趣
2. 长度适中（100-200字）
3. 语气要${style}
4. 避免重复和机械化的表达

请直接返回帖子内容，不要包含其他说明：`;

    const content = await service.generateText(prompt, config.model);
    return content.trim();

  } catch (error) {
    console.error('生成帖子内容失败:', error);
    throw error;
  }
}

// 示例5: 评论生成
export async function generateComment(postContent: string, commentStyle: string = '支持') {
  try {
    const config = await loadOpenAIConfig();
    const service = new OpenAIService(config.apiKey!, config.baseURL);

    const prompt = `请根据以下帖子内容生成一个${commentStyle}的评论：

帖子内容: ${postContent}

评论要求：
1. 与帖子内容相关
2. 表达${commentStyle}的态度
3. 长度适中（50-100字）
4. 语气自然，不要太正式
5. 避免重复和模板化

请直接返回评论内容：`;

    const comment = await service.generateText(prompt, config.model);
    return comment.trim();

  } catch (error) {
    console.error('生成评论失败:', error);
    throw error;
  }
}

// 示例6: 配置管理
export async function configExample() {
  try {
    // 保存配置
    await saveOpenAIConfig({
      apiKey: 'your-api-key-here',
      model: OPENAI_MODELS.GPT_4O_MINI,
      maxTokens: 500,
      temperature: 0.8
    });

    // 加载配置
    const config = await loadOpenAIConfig();
    console.log('当前配置:', config);

  } catch (error) {
    console.error('配置管理失败:', error);
  }
}

// 示例7: 在 Content Script 中使用
export async function contentScriptExample() {
  // 这个函数可以在 content script 中调用
  // 用于分析页面内容并生成相应的回复
  
  try {
    // 获取页面标题
    const pageTitle = document.title;
    
    // 获取页面主要内容（示例）
    const mainContent = document.querySelector('main, article, .content')?.textContent?.slice(0, 500) || '';

    // 生成相关的评论或回复
    const comment = await generateComment(
      `页面标题: ${pageTitle}\n内容摘要: ${mainContent}`,
      '有见地'
    );

    console.log('生成的评论:', comment);
    return comment;

  } catch (error) {
    console.error('Content Script 中使用 OpenAI 失败:', error);
    throw error;
  }
}
