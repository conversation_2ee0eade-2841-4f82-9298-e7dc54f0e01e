# OpenAI 集成指南

本文档介绍如何在 BrowseForMe 浏览器扩展中使用 OpenAI 功能。

## 功能概述

- ✅ OpenAI API 集成
- ✅ 多模型支持 (GPT-3.5, GPT-4, GPT-4o 等)
- ✅ 流式响应支持
- ✅ 配置管理和持久化
- ✅ 浏览器扩展环境优化
- ✅ 错误处理和重试机制

## 快速开始

### 1. 安装依赖

```bash
pnpm add openai @radix-ui/react-select @radix-ui/react-slider
```

### 2. 配置 API Key

在扩展的设置页面中配置你的 OpenAI API Key：

```typescript
import { saveOpenAIConfig } from './lib/openaiConfig';

await saveOpenAIConfig({
  apiKey: 'your-openai-api-key',
  model: 'gpt-4o-mini',
  maxTokens: 1000,
  temperature: 0.7
});
```

### 3. 基本使用

```typescript
import { initializeOpenAI, generateText } from './lib/openaiService';
import { loadOpenAIConfig } from './lib/openaiConfig';

// 初始化
const config = await loadOpenAIConfig();
initializeOpenAI(config.apiKey, config.baseURL);

// 生成文本
const response = await generateText('请帮我写一个产品评论');
console.log(response);
```

## 核心文件说明

### `lib/openaiService.ts`
OpenAI 服务的核心实现，包含：
- `OpenAIService` 类：封装 OpenAI API 调用
- `generateText()`: 简单文本生成
- `generateChatCompletion()`: 聊天对话
- `generateChatCompletionStream()`: 流式生成

### `lib/openaiConfig.ts`
配置管理，包含：
- 配置接口定义
- 存储和加载配置
- 配置验证
- 默认值和常量

### `components/OpenAISettings.tsx`
React 设置组件，提供：
- API Key 配置
- 模型选择
- 参数调整
- 连接测试

### `examples/openaiUsage.ts`
使用示例，包含：
- 基本用法
- 聊天对话
- 流式生成
- 内容生成（适用于刷帖）

## 使用场景

### 1. 生成帖子内容

```typescript
import { generatePostContent } from './examples/openaiUsage';

const content = await generatePostContent('人工智能的发展', '友好');
console.log('生成的帖子:', content);
```

### 2. 生成评论回复

```typescript
import { generateComment } from './examples/openaiUsage';

const comment = await generateComment('这是一个很棒的产品！', '支持');
console.log('生成的评论:', comment);
```

### 3. 在 Content Script 中使用

```typescript
// content script 中
import { contentScriptExample } from './examples/openaiUsage';

// 分析页面内容并生成相关评论
const comment = await contentScriptExample();
```

### 4. 流式生成（实时显示）

```typescript
import { OpenAIService } from './lib/openaiService';

const service = new OpenAIService(apiKey);
await service.generateChatCompletionStream(
  [{ role: 'user', content: '写一首诗' }],
  'gpt-4o-mini',
  (chunk) => {
    // 实时显示生成的内容
    console.log(chunk);
  }
);
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `apiKey` | string | - | OpenAI API Key（必需） |
| `baseURL` | string | `https://api.openai.com/v1` | API 基础 URL |
| `model` | string | `gpt-4o-mini` | 使用的模型 |
| `maxTokens` | number | `1000` | 最大 token 数 |
| `temperature` | number | `0.7` | 创造性参数 (0-2) |

## 支持的模型

- `gpt-4o-mini` - 最新的小型模型，性价比高
- `gpt-4o` - 最新的大型模型
- `gpt-4-turbo-preview` - GPT-4 Turbo
- `gpt-4` - 标准 GPT-4
- `gpt-3.5-turbo` - GPT-3.5 Turbo

## 错误处理

```typescript
try {
  const response = await generateText('你好');
} catch (error) {
  if (error.status === 401) {
    console.error('API Key 无效');
  } else if (error.status === 429) {
    console.error('请求频率过高');
  } else {
    console.error('其他错误:', error.message);
  }
}
```

## 最佳实践

1. **API Key 安全**: 不要在代码中硬编码 API Key，使用配置管理
2. **错误处理**: 始终包装 API 调用在 try-catch 中
3. **请求频率**: 注意 OpenAI 的速率限制
4. **Token 管理**: 合理设置 maxTokens 以控制成本
5. **模型选择**: 根据需求选择合适的模型

## 故障排除

### 常见问题

1. **API Key 无效**
   - 检查 API Key 是否正确
   - 确认账户有足够余额

2. **网络连接问题**
   - 检查网络连接
   - 尝试使用代理或自定义 baseURL

3. **请求被拒绝**
   - 检查请求内容是否符合 OpenAI 政策
   - 减少请求频率

4. **浏览器扩展权限**
   - 确保 manifest.json 中有正确的权限配置
   - 检查 CSP 设置

### 调试技巧

```typescript
// 启用详细日志
console.log('发送请求:', { messages, model, maxTokens });

// 检查配置
const config = await loadOpenAIConfig();
console.log('当前配置:', config);

// 测试连接
await handleTest(); // 在设置页面中
```

## 更新日志

- v1.0.0: 初始版本，支持基本的 OpenAI API 集成
- 支持多模型选择和参数配置
- 提供完整的 React 设置界面
- 包含使用示例和最佳实践
