[{"title": "为什么 ai 写前端界面总是偏向使用高饱和紫色，毛玻璃还有淡入淡出动效", "xpath": "//*[@id='Main']/div[2]/div[3]", "author": "(无作者)", "time": "1 分钟前", "link": "https://www.v2ex.com/member/404www"}, {"title": "如何看待 v 站今日热议全是空投？", "xpath": "//*[@id='Main']/div[2]/div[4]", "author": "(无作者)", "time": "2 分钟前", "link": "https://www.v2ex.com/member/AoEiuV020JP"}, {"title": "大道至简，把项目中的设计模式、多继承、各种抽象类全干掉了，只保留了单例，舒服了。当年年轻的时候总是想办法写的优雅，归来仍是少年。", "xpath": "//*[@id='Main']/div[2]/div[5]", "author": "(无作者)", "time": "11 分钟前", "link": "https://www.v2ex.com/member/ajaxgoldfish0"}, {"title": "重磅更新！ Claude Code 引入大杀器 sub-agents", "xpath": "//*[@id='Main']/div[2]/div[6]", "author": "(无作者)", "time": "8 分钟前", "link": "https://www.v2ex.com/member/terryso"}, {"title": "随手写了一个油猴脚本，优化 google ai studio 样式", "xpath": "//*[@id='Main']/div[2]/div[7]", "author": "(无作者)", "time": "25 分钟前", "link": "https://www.v2ex.com/member/bronyakaka"}, {"title": "go 微服务项目是否可以将 nacos-go 服务端放到项目里直接启动", "xpath": "//*[@id='Main']/div[2]/div[8]", "author": "(无作者)", "time": "28 分钟前", "link": "https://www.v2ex.com/member/ohohohh"}, {"title": "请教下面向境内的图片存储/CDN 的便宜方案", "xpath": "//*[@id='Main']/div[2]/div[9]", "author": "(无作者)", "time": "39 分钟前", "link": "https://www.v2ex.com/member/heliumjt"}, {"title": "各位觉得 Qwen3 Coder 480B 的实际表现怎么样? 可以替代 Claude-4-Sonnet/Opus 作为 Claude Code 的调用模型吗?", "xpath": "//*[@id='Main']/div[2]/div[10]", "author": "(无作者)", "time": "57 分钟前", "link": "https://www.v2ex.com/member/Int100"}, {"title": "求推荐， X（Twitter）的爬虫 API 付费平台（有价值的回答，打赏 SOL）", "xpath": "//*[@id='Main']/div[2]/div[11]", "author": "(无作者)", "time": "37 分钟前", "link": "https://www.v2ex.com/member/LemonLeon"}, {"title": "小团队开发测试环境怎么解决", "xpath": "//*[@id='Main']/div[2]/div[12]", "author": "(无作者)", "time": "11 分钟前", "link": "https://www.v2ex.com/member/guoguobaba"}, {"title": "儿童编程", "xpath": "//*[@id='Main']/div[2]/div[13]", "author": "(无作者)", "time": "30 分钟前", "link": "https://www.v2ex.com/member/minchieh"}, {"title": "网页浏览器新型狗皮膏药", "xpath": "//*[@id='Main']/div[2]/div[14]", "author": "(无作者)", "time": "22 分钟前", "link": "https://www.v2ex.com/member/xmz8888"}, {"title": "最近小红书找了一个 claude code，总感觉被坑了", "xpath": "//*[@id='Main']/div[2]/div[15]", "author": "(无作者)", "time": "32 分钟前", "link": "https://www.v2ex.com/member/fingerxie"}, {"title": "在群晖上运行 podman，有人试过吗？", "xpath": "//*[@id='Main']/div[2]/div[16]", "author": "(无作者)", "time": "21 分钟前", "link": "https://www.v2ex.com/member/opengg"}, {"title": "干了几年程序员，不会独立开发项目（也没机会），怎么办", "xpath": "//*[@id='Main']/div[2]/div[17]", "author": "(无作者)", "time": "39 分钟前", "link": "https://www.v2ex.com/member/gufeng311"}, {"title": "面个小小厂，问一堆性能优化问题，你们厂天天搞性能优化吗？", "xpath": "//*[@id='Main']/div[2]/div[18]", "author": "(无作者)", "time": "10 分钟前", "link": "https://www.v2ex.com/member/DeepUse"}, {"title": "[GitHub 开源] 不写一行代码最快 1 分钟部署一个交互友好炫酷全自动化管理你的 GitHub Star 项目的网站", "xpath": "//*[@id='Main']/div[2]/div[19]", "author": "(无作者)", "time": "12 分钟前", "link": "https://www.v2ex.com/member/tonngw"}, {"title": "关于 Trae 自定义 Gemini 时一直转圈无法正常设置的问题", "xpath": "//*[@id='Main']/div[2]/div[20]", "author": "(无作者)", "time": "29 分钟前", "link": "https://www.v2ex.com/member/<PERSON>aina"}, {"title": "它是如何通过非常规手段添加“美团视频”到桌面", "xpath": "//*[@id='Main']/div[2]/div[21]", "author": "(无作者)", "time": "8 分钟前", "link": "https://www.v2ex.com/member/busier"}, {"title": "Flutter 社会分享 Instagram WhatsApp-有哪些可靠方案？", "xpath": "//*[@id='Main']/div[2]/div[22]", "author": "(无作者)", "time": "2 分钟前", "link": "https://www.v2ex.com/member/Yangpengxu"}, {"title": "有一个 8K 行的 VUE 代码文件用什么 AI 可以解耦", "xpath": "//*[@id='Main']/div[2]/div[23]", "author": "(无作者)", "time": "17 分钟前", "link": "https://www.v2ex.com/member/ljlljl0"}, {"title": "安卓电视上用什么播放器，播放 nas 中 emby 服务器上的电视？", "xpath": "//*[@id='Main']/div[2]/div[24]", "author": "(无作者)", "time": "22 分钟前", "link": "https://www.v2ex.com/member/rainfy"}, {"title": "SQL 数据库咨询", "xpath": "//*[@id='Main']/div[2]/div[25]", "author": "(无作者)", "time": "37 分钟前", "link": "https://www.v2ex.com/member/soustinafey"}, {"title": "推荐一款非常好看的 Claude Code 应用: Conductor", "xpath": "//*[@id='Main']/div[2]/div[26]", "author": "(无作者)", "time": "28 分钟前", "link": "https://www.v2ex.com/member/terryso"}, {"title": "怎样才算是一个优秀的微服务", "xpath": "//*[@id='Main']/div[2]/div[27]", "author": "(无作者)", "time": "39 分钟前", "link": "https://www.v2ex.com/member/rehoni"}, {"title": "做官网 需要 seo，大家使用 next.js 还是 vue+nuxt.js 还是直接 html？", "xpath": "//*[@id='Main']/div[2]/div[28]", "author": "(无作者)", "time": "54 分钟前", "link": "https://www.v2ex.com/member/leonhan31"}, {"title": "V 友们，有没有那种聚合各种 restful API 的服务？", "xpath": "//*[@id='Main']/div[2]/div[29]", "author": "(无作者)", "time": "34 分钟前", "link": "https://www.v2ex.com/member/somebody1"}, {"title": "安卓上的高德地图是不是限制了帧率", "xpath": "//*[@id='Main']/div[2]/div[30]", "author": "(无作者)", "time": "49 分钟前", "link": "https://www.v2ex.com/member/ZingLix"}, {"title": "请教使用 AI 提升效率的实践", "xpath": "//*[@id='Main']/div[2]/div[31]", "author": "(无作者)", "time": "11 分钟前", "link": "https://www.v2ex.com/member/daifee"}, {"title": "禁掉了 AI 代码补全，返璞归真，享受原汁原味的 IDE～", "xpath": "//*[@id='Main']/div[2]/div[32]", "author": "(无作者)", "time": "46 分钟前", "link": "https://www.v2ex.com/member/BaymaxK"}, {"title": "需求是要求前端研究一下如何给登录认证自动续期", "xpath": "//*[@id='Main']/div[2]/div[33]", "author": "(无作者)", "time": "13 分钟前", "link": "https://www.v2ex.com/member/11000111010"}, {"title": "各位大佬 帮忙看看怎么实现做网站的自动部署？", "xpath": "//*[@id='Main']/div[2]/div[34]", "author": "(无作者)", "time": "35 分钟前", "link": "https://www.v2ex.com/member/hugozach"}, {"title": "Gitops 中涉及敏感信息推荐用什么工具呢？", "xpath": "//*[@id='Main']/div[2]/div[35]", "author": "(无作者)", "time": "47 分钟前", "link": "https://www.v2ex.com/member/COW"}, {"title": "阿里发布代码模型 Qwen3-Coder", "xpath": "//*[@id='Main']/div[2]/div[36]", "author": "(无作者)", "time": "32 分钟前", "link": "https://www.v2ex.com/member/yuyue001"}, {"title": "有 1 个使用了差不多 5 年的谷歌号被封了,有办法解开吗", "xpath": "//*[@id='Main']/div[2]/div[37]", "author": "(无作者)", "time": "6 分钟前", "link": "https://www.v2ex.com/member/pnczk2019"}, {"title": "没懂， Trae 的 SOLO 模式到底能干点啥？", "xpath": "//*[@id='Main']/div[2]/div[38]", "author": "(无作者)", "time": "57 分钟前", "link": "https://www.v2ex.com/member/icev5"}, {"title": "私有化部署 OCR 解决方案请教", "xpath": "//*[@id='Main']/div[2]/div[39]", "author": "(无作者)", "time": "24 分钟前", "link": "https://www.v2ex.com/member/Sh1xin"}, {"title": "Mac 上 vsc 使用小鹤音形输入不了中文求助一下", "xpath": "//*[@id='Main']/div[2]/div[40]", "author": "(无作者)", "time": "13 分钟前", "link": "https://www.v2ex.com/member/3657iverson"}, {"title": "zip 包忘记密码，怎么找回？ zip2john hash 太大了 hashcat 不支持， john 太慢了，还有什么思路？", "xpath": "//*[@id='Main']/div[2]/div[41]", "author": "(无作者)", "time": "15 分钟前", "link": "https://www.v2ex.com/member/m3bro"}, {"title": "怎么参与一下开源项目或者怎么提升代码能力呢？", "xpath": "//*[@id='Main']/div[2]/div[42]", "author": "(无作者)", "time": "10 分钟前", "link": "https://www.v2ex.com/member/mxnoir"}, {"title": "AI 焦虑症候群段子，太典了", "xpath": "//*[@id='Main']/div[2]/div[43]", "author": "(无作者)", "time": "2 分钟前", "link": "https://www.v2ex.com/member/Wh0amis"}, {"title": "有支持蓝牙 LE Audio 的耳机吗？", "xpath": "//*[@id='Main']/div[2]/div[44]", "author": "(无作者)", "time": "6 分钟前", "link": "https://www.v2ex.com/member/wuruxu"}, {"title": "求助：如何获取才能到手机传感器参数？", "xpath": "//*[@id='Main']/div[2]/div[45]", "author": "(无作者)", "time": "13 分钟前", "link": "https://www.v2ex.com/member/Incarna"}, {"title": "2025 年了，求推荐免费的在线 IDE，不要求功能多复杂高级，有基本的工程管理、代码高亮 这些就行", "xpath": "//*[@id='Main']/div[2]/div[46]", "author": "(无作者)", "time": "39 分钟前", "link": "https://www.v2ex.com/member/xFrank"}, {"title": "群晖 nas 里的 Docker 怎么备份 有没有类似快照的备份", "xpath": "//*[@id='Main']/div[2]/div[47]", "author": "(无作者)", "time": "42 分钟前", "link": "https://www.v2ex.com/member/turlin"}, {"title": "qwen code 可以平替 gemini cli 了吗", "xpath": "//*[@id='Main']/div[2]/div[48]", "author": "(无作者)", "time": "46 分钟前", "link": "https://www.v2ex.com/member/blababa"}, {"title": "kiro 的所谓 spec 模型好像并没说的那么好", "xpath": "//*[@id='Main']/div[2]/div[49]", "author": "(无作者)", "time": "38 分钟前", "link": "https://www.v2ex.com/member/iorilu"}, {"title": "黄仁勋：我总感觉公司快要倒闭了", "xpath": "//*[@id='Main']/div[2]/div[50]", "author": "(无作者)", "time": "32 分钟前", "link": "https://www.v2ex.com/member/gufeng311"}, {"title": "你认为什么规模的公司适合使用 k8s?", "xpath": "//*[@id='Main']/div[2]/div[51]", "author": "(无作者)", "time": "40 分钟前", "link": "https://www.v2ex.com/member/linxuan716"}, {"title": "csharp 这回真成了脚本语言： dotnet run app.cs", "xpath": "//*[@id='Main']/div[2]/div[52]", "author": "(无作者)", "time": "21 分钟前", "link": "https://www.v2ex.com/member/Need4more"}, {"title": "[空投] 评论区抽 3 位用户空投 0.01sol", "xpath": "//*[@class='item_hot_topic_title']", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147332"}, {"title": "「空投」抽 28 人送 0.001 SOL（突然意识到打赏系统的上线让铜币有了价值）", "xpath": "//*[@id='TopicsHot']/div[3]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147391"}, {"title": "[空投] 最近开心，来以币（铜币）换币（0.001 sol）了", "xpath": "//*[@id='TopicsHot']/div[5]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147403"}, {"title": "[空投] 明牌赚铜币，评论区抽 20 位用户空投 0.001sol", "xpath": "//*[@id='TopicsHot']/div[6]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147432"}, {"title": "体验打赏功能，绑定了钱包的留言互动，抽 10 人每人送 0.001 SOL，随机抽。", "xpath": "//*[@id='TopicsHot']/div[8]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147547"}, {"title": "[空投] 明牌赚铜币，评论区抽 10 位用户空投 0.001sol", "xpath": "//*[@id='TopicsHot']/div[9]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147472"}, {"title": "「空投」抽 20 人送 0.001 SOL -- 明牌赚铜币", "xpath": "//*[@id='TopicsHot']/div[10]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147494"}, {"title": "如何看待近期懂车帝高速驾驶辅助测试榜单", "xpath": "//*[@id='TopicsHot']/div[11]/table/tbody/tr/td[3]/span", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147320"}, {"title": "大道至简，把项目中的设计模式、多继承、各种抽象类全干掉了，只保留了单例，舒服了。当年年轻的时候总是想办法写的优雅，归来仍是少年。", "xpath": "//*[@id='Main']/div[2]/div[5]/table/tbody/tr/td[3]/span[1]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147369#reply40"}, {"title": "各位觉得 Qwen3 Coder 480B 的实际表现怎么样? 可以替代 Claude-4-Sonnet/Opus 作为 Claude Code 的调用模型吗?", "xpath": "//*[@id='Main']/div[2]/div[10]/table/tbody/tr/td[3]/span[1]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147506#reply17"}, {"title": "[GitHub 开源] 不写一行代码最快 1 分钟部署一个交互友好炫酷全自动化管理你的 GitHub Star 项目的网站", "xpath": "//*[@id='Main']/div[2]/div[19]/table/tbody/tr/td[3]/span[1]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147516#reply2"}, {"title": "zip 包忘记密码，怎么找回？ zip2john hash 太大了 hashcat 不支持， john 太慢了，还有什么思路？", "xpath": "//*[@id='Main']/div[2]/div[41]/table/tbody/tr/td[3]/span[1]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147470#reply2"}, {"title": "2025 年了，求推荐免费的在线 IDE，不要求功能多复杂高级，有基本的工程管理、代码高亮 这些就行", "xpath": "//*[@id='Main']/div[2]/div[46]/table/tbody/tr/td[3]/span[1]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/t/1147182#reply19"}, {"title": "0 未读提醒", "xpath": "//*[@id='Rightbar']/div[2]/div[3]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/notifications"}, {"title": "VXNA", "xpath": "//*[@id='Rightbar']/div[14]/div[1]", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/xna"}, {"title": "𝑻𝒉𝒊𝒓𝒕𝒆𝒆𝒏", "xpath": "//*[@id='Rightbar']/div[14]/div[2]", "author": "(无作者)", "time": "(无时间)", "link": "https://blog.oospace.com/"}, {"title": "(无标题)", "xpath": "//*[@class='content']", "author": "(无作者)", "time": "(无时间)", "link": "https://www.v2ex.com/"}, {"title": "[空投] 评论区抽 3 位用户空投 0.01sol\n                预览", "xpath": "//*[@id='Wrapper']/div[1]", "author": "(无作者)", "time": "1 分钟前", "link": "https://www.v2ex.com/member/kongkongye"}, {"title": "(无标题)", "xpath": "//*[@id='Bottom']/div", "author": "(无作者)", "time": "(无时间)", "link": "https://www.digitalocean.com/?refcode=1b51f1a7651d"}]