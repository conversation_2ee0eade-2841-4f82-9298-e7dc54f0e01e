/**
 * OpenAI 集成测试
 * 注意：这些测试需要有效的 API Key 才能运行
 */

import { OpenAIService, initializeOpenAI } from '../lib/openaiService';
import {
  validateOpenAIConfig,
  OPENAI_MODELS
} from '../lib/openaiConfig';

describe('OpenAI Configuration', () => {
  test('should validate config correctly', () => {
    const validConfig = {
      apiKey: 'sk-test123',
      model: OPENAI_MODELS.GPT_4O_MINI,
      maxTokens: 1000,
      temperature: 0.7
    };

    const errors = validateOpenAIConfig(validConfig);
    expect(errors).toHaveLength(0);
  });

  test('should detect invalid config', () => {
    const invalidConfig = {
      apiKey: '',
      maxTokens: -1,
      temperature: 3
    };

    const errors = validateOpenAIConfig(invalidConfig);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors).toContain('API Key 不能为空');
  });
});

describe('OpenAI Service', () => {
  test('should create service instance', () => {
    const service = new OpenAIService('sk-test123', 'https://api.openai.com/v1');
    expect(service).toBeInstanceOf(OpenAIService);
  });

  test('should initialize default service', () => {
    expect(() => {
      initializeOpenAI('sk-test123');
    }).not.toThrow();
  });
});

describe('Integration Examples', () => {
  test('should validate example usage patterns', () => {
    // 测试配置验证
    const config = {
      apiKey: 'sk-test123',
      model: OPENAI_MODELS.GPT_4O_MINI,
      maxTokens: 1000,
      temperature: 0.7
    };

    const errors = validateOpenAIConfig(config);
    expect(errors).toHaveLength(0);

    // 测试服务创建
    const service = new OpenAIService(config.apiKey);
    expect(service).toBeInstanceOf(OpenAIService);
  });
});

// 性能测试
describe('Performance', () => {
  test('should handle multiple concurrent requests', async () => {
    // 创建多个并发请求（模拟）
    const promises = Array(5).fill(null).map(() =>
      Promise.resolve('mock response')
    );

    const results = await Promise.all(promises);
    expect(results).toHaveLength(5);
  });

  test('should handle large text inputs', () => {
    const largeText = 'A'.repeat(10000);
    const service = new OpenAIService('sk-test123');

    // 验证服务可以处理大文本（不实际发送请求）
    expect(() => {
      // 这里只是验证参数处理，不实际调用 API
      const messages = [{ role: 'user' as const, content: largeText }];
      expect(messages[0].content.length).toBe(10000);
    }).not.toThrow();
  });
});
