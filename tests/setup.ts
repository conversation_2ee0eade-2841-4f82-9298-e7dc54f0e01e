// Jest 测试设置文件

// 模拟 Chrome 扩展 API
const mockChrome = {
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn()
    },
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn()
    }
  },
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  tabs: {
    query: jest.fn(),
    sendMessage: jest.fn()
  }
};

// @ts-ignore
global.chrome = mockChrome;

// 模拟 fetch API（如果需要）
global.fetch = jest.fn();

// 设置默认的模拟返回值
beforeEach(() => {
  jest.clearAllMocks();
  
  // 设置默认的存储模拟
  mockChrome.storage.sync.get.mockResolvedValue({});
  mockChrome.storage.sync.set.mockResolvedValue(undefined);
  mockChrome.storage.local.get.mockResolvedValue({});
  mockChrome.storage.local.set.mockResolvedValue(undefined);
});

// 全局测试工具函数
const createMockConfig = () => ({
  apiKey: 'sk-test123',
  baseURL: 'https://api.openai.com/v1',
  model: 'gpt-4o-mini',
  maxTokens: 1000,
  temperature: 0.7
});

const mockStorageData = (data: Record<string, any>) => {
  mockChrome.storage.sync.get.mockResolvedValue(data);
};

// 将函数添加到全局对象
(global as any).createMockConfig = createMockConfig;
(global as any).mockStorageData = mockStorageData;
